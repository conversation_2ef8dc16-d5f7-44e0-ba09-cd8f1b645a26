/**
 * Workflow Results Page
 * Display the generated content from a completed workflow
 */

'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

// Error Boundary Component
function ErrorBoundary({ children, fallback }: { children: React.ReactNode; fallback: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Error caught by boundary:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

interface WorkflowResults {
  execution: {
    id: string;
    workflowId: string;
    status: string;
    progress: number;
    startedAt: string;
    completedAt?: string;
    inputs: Record<string, any>;
    currentStep?: string;
  };
  workflow: {
    id: string;
    name: string;
    description: string;
  } | null;
  steps: Array<{
    stepId: string;
    status: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    inputs: Record<string, any>;
    outputs: Record<string, any>;
    error?: string;
    stepType?: string;
    approvalRequired?: boolean;
    artifactId?: string;
  }>;
  content: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    createdAt: string;
    metadata?: Record<string, any>;
  }>;
  artifacts: Array<{
    id: string;
    type: string;
    title: string;
    content: any;
    status: string;
    stepId: string;
    executionId: string;
    createdAt: string;
    approvedBy?: string;
    approvedAt?: string;
  }>;
}

function WorkflowResultsPageContent() {
  const params = useParams();
  const router = useRouter();
  const executionId = params.id as string;

  const [results, setResults] = useState<WorkflowResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [publishingStatus, setPublishingStatus] = useState<Record<string, 'idle' | 'publishing' | 'published' | 'failed'>>({});
  const [publishResults, setPublishResults] = useState<Record<string, { url?: string; error?: string }>>({});
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && executionId) {
      loadResults();
    }
  }, [executionId, mounted]);

  const loadResults = async () => {
    if (!executionId) {
      setError('No execution ID provided');
      setLoading(false);
      return;
    }

    try {
      // Use the correct Workflow Results API
      const response = await fetch(`/api/workflow/results/${executionId}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Debug: Log the received data
        console.log('🔍 Results API Response:', result.data);

        // The data is already in the correct format from the results API
        const execution = result.data.execution;
        const workflow = result.data.workflow;
        const artifacts = result.data.artifacts || [];
        const steps = result.data.steps || [];

        console.log('📊 Parsed data:', {
          execution: execution?.id,
          workflow: workflow?.name,
          artifactsCount: artifacts.length,
          stepsCount: steps.length,
          artifacts: artifacts.map((a: any) => ({ id: a.id, title: a.title, status: a.status }))
        });

        // Ensure all required fields exist with safe defaults
        const transformedResults: WorkflowResults = {
          execution: {
            id: execution?.id || executionId,
            workflowId: execution?.workflowId || 'unknown',
            status: execution?.status || 'unknown',
            progress: execution?.progress || 0,
            startedAt: execution?.startedAt || new Date().toISOString(),
            completedAt: execution?.completedAt,
            inputs: execution?.inputs || {},
            currentStep: execution?.currentStep
          },
          workflow: workflow ? {
            id: workflow.id || 'unknown',
            name: workflow.name || 'Unknown Workflow',
            description: workflow.description || 'No description available'
          } : null,
          steps: steps.length > 0 ? steps : Object.values(execution?.stepResults || {}).map((step: any) => ({
            stepId: step?.stepId || 'unknown',
            status: step?.status || 'unknown',
            startedAt: step?.startedAt || new Date().toISOString(),
            completedAt: step?.completedAt,
            duration: step?.duration,
            inputs: step?.inputs || {},
            outputs: step?.outputs || {},
            error: step?.error,
            stepType: step?.stepType,
            approvalRequired: step?.approvalRequired,
            artifactId: step?.artifactId
          })),
          content: [], // Legacy content - will be replaced by artifacts
          artifacts: artifacts.map((artifact: any) => ({
            ...artifact,
            id: artifact.id || `artifact-${Date.now()}`,
            title: artifact.title || 'Untitled Artifact',
            content: artifact.content || '',
            status: artifact.status || 'draft',
            stepId: artifact.stepId || 'unknown',
            executionId: artifact.executionId || executionId,
            createdAt: artifact.createdAt || new Date().toISOString()
          }))
        };

        setResults(transformedResults);
      } else {
        setError(result.error || 'Failed to load results');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load results';
      setError(errorMessage);
      console.error('Load results error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatContent = (content: any): string => {
    if (!content) return 'No content available';
    if (typeof content === 'string') {
      return content;
    }
    try {
      return JSON.stringify(content, null, 2);
    } catch (err) {
      return 'Error formatting content';
    }
  };

  const formatDuration = (duration?: number): string => {
    if (!duration || typeof duration !== 'number') return 'N/A';
    return `${(duration / 1000).toFixed(1)}s`;
  };

  const formatDate = (dateString?: string): string => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleString();
    } catch (err) {
      return 'Invalid date';
    }
  };

  const publishToCMS = async (artifactId: string, artifact: any) => {
    setPublishingStatus(prev => ({ ...prev, [artifactId]: 'publishing' }));

    try {
      // Publish to CMS (Payload CMS integration)
      const response = await fetch(`/api/cms/publish`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          artifactId,
          title: artifact.title,
          content: artifact.content,
          type: artifact.type,
          executionId: artifact.executionId,
          stepId: artifact.stepId,
          metadata: {
            workflowGenerated: true,
            originalArtifactId: artifactId,
            generatedAt: artifact.createdAt,
            approvedBy: artifact.approvedBy,
            approvedAt: artifact.approvedAt
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'published' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { url: result.data?.url || result.data?.id }
        }));
        setError('');
      } else {
        setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
        setPublishResults(prev => ({
          ...prev,
          [artifactId]: { error: result.error || 'Failed to publish' }
        }));
      }
    } catch (err) {
      setPublishingStatus(prev => ({ ...prev, [artifactId]: 'failed' }));
      setPublishResults(prev => ({
        ...prev,
        [artifactId]: { error: 'Failed to publish to CMS' }
      }));
      console.error('CMS publish error:', err);
    }
  };

  const downloadArtifact = (artifact: any) => {
    const content = typeof artifact.content === 'string'
      ? artifact.content
      : JSON.stringify(artifact.content, null, 2);

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${artifact.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const isPublishable = (artifact: any): boolean => {
    return artifact.status === 'approved';
  };

  const getPublishStatus = (artifactId: string): string => {
    const status = publishingStatus[artifactId] || 'idle';
    switch (status) {
      case 'publishing': return 'Publishing...';
      case 'published': return 'Published';
      case 'failed': return 'Failed';
      default: return 'Ready to Publish';
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-700';
      case 'rejected': return 'bg-red-100 text-red-700';
      case 'pending_approval': return 'bg-yellow-100 text-yellow-700';
      case 'draft': return 'bg-gray-100 text-gray-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Initializing...</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <h3 className="font-bold">Error Loading Results</h3>
          <p>{error}</p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            🔄 Retry
          </button>
          <button
            onClick={() => router.push('/workflow/agent-enhanced')}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            ← Back to Workflow
          </button>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="max-w-6xl mx-auto p-6">
        <div className="text-center py-12">
          <p className="text-gray-600">No results found for execution ID: {executionId}</p>
          <button
            onClick={() => router.push('/workflow/agent-enhanced')}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            ← Back to Workflow
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Workflow Results</h1>
            <p className="text-gray-600 mb-3">View approved artifacts and publish them to your CMS</p>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Execution ID: {results.execution.id}</span>
              <span>Status: <span className={`font-medium ${
                results.execution.status === 'completed' ? 'text-green-600' :
                results.execution.status === 'failed' ? 'text-red-600' :
                'text-blue-600'
              }`}>{results.execution.status}</span></span>
              <span>Progress: {results.execution.progress}%</span>
            </div>
          </div>

          <div className="flex gap-3">
            <button
              onClick={() => router.push('/workflow')}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              ← Back to Workflow
            </button>
            <button
              onClick={() => router.push('/dashboard')}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Dashboard
            </button>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
            >
              🔄 Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Workflow Info */}
      {results.workflow && (
        <div className="bg-gray-50 p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Workflow: {results.workflow.name}</h3>
          <p className="text-sm text-gray-600">{results.workflow.description}</p>
        </div>
      )}

      {/* Execution Summary */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h3 className="font-medium mb-3">Execution Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Started:</span>
            <p>{formatDate(results.execution.startedAt)}</p>
          </div>
          {results.execution.completedAt && (
            <div>
              <span className="text-gray-500">Completed:</span>
              <p>{formatDate(results.execution.completedAt)}</p>
            </div>
          )}
          <div>
            <span className="text-gray-500">Total Steps:</span>
            <p>{results.steps?.length || 0}</p>
          </div>
          <div>
            <span className="text-gray-500">Artifacts:</span>
            <p>{results.artifacts?.length || 0}</p>
          </div>
        </div>
      </div>

      {/* Publishing Status Summary */}
      {results.artifacts.length > 0 && (
        <div className="bg-white border rounded-lg p-4 mb-6">
          <h3 className="font-medium mb-3">Publishing Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">Ready to Publish:</span>
              <p className="text-blue-600 font-medium">
                {results.artifacts.filter(a => isPublishable(a) && !publishingStatus[a.id]).length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Published:</span>
              <p className="text-green-600 font-medium">
                {Object.values(publishingStatus).filter(status => status === 'published').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Failed:</span>
              <p className="text-red-600 font-medium">
                {Object.values(publishingStatus).filter(status => status === 'failed').length}
              </p>
            </div>
            <div>
              <span className="text-gray-500">Total Artifacts:</span>
              <p className="font-medium">
                {results.artifacts.length}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Agent Consultation Results */}
      {results.steps && results.steps.some(step => step.outputs?.agentInsights) && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4">🤖 Agent Consultation Results</h3>
          <div className="space-y-4">
            {results.steps
              .filter(step => step.outputs?.agentInsights)
              .map((step, index) => {
                const insights = step.outputs.agentInsights || {};
                const summary = step.outputs.consultationSummary;

                return (
                  <div key={`agent-${step.stepId}-${index}`} className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-medium text-blue-900">{step.stepId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h4>
                      {summary && (
                        <div className="flex items-center space-x-3 text-sm text-blue-700">
                          <span className="bg-blue-100 px-2 py-1 rounded">🤖 {summary.totalConsultations} consultations</span>
                          <span className="bg-green-100 px-2 py-1 rounded">📊 {Math.round(summary.averageConfidence * 100)}% confidence</span>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {Object.entries(insights).map(([agentId, agentInsight]: [string, any]) => (
                        <div key={agentId} className="bg-white rounded-lg p-4 border border-blue-200">
                          <h5 className="font-medium text-blue-900 mb-3 capitalize flex items-center">
                            {agentId === 'seo-keyword' && '🔍'}
                            {agentId === 'content-strategy' && '📋'}
                            {agentId === 'market-research' && '📊'}
                            <span className="ml-2">{agentId.replace('-', ' ')} Agent</span>
                          </h5>

                          {/* Agent Artifacts Summary */}
                          {agentId === 'seo-keyword' && (
                            <div className="space-y-2 text-sm">
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">Keywords Found</div>
                                <div className="text-blue-600">5 primary, 7 long-tail</div>
                              </div>
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">SEO Score</div>
                                <div className="text-green-600">85/100</div>
                              </div>
                            </div>
                          )}

                          {agentId === 'content-strategy' && (
                            <div className="space-y-2 text-sm">
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">Content Structure</div>
                                <div className="text-blue-600">7 sections planned</div>
                              </div>
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">Content Pillars</div>
                                <div className="text-purple-600">3 strategic pillars</div>
                              </div>
                            </div>
                          )}

                          {agentId === 'market-research' && (
                            <div className="space-y-2 text-sm">
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">Market Size</div>
                                <div className="text-green-600">$997.77B by 2028</div>
                              </div>
                              <div className="bg-gray-50 p-2 rounded">
                                <div className="font-medium text-gray-700">Growth Rate</div>
                                <div className="text-blue-600">40.2% CAGR</div>
                              </div>
                            </div>
                          )}

                          {agentInsight.keyRecommendations && (
                            <div className="mt-3 bg-yellow-50 p-2 rounded border border-yellow-200">
                              <div className="font-medium text-yellow-900 text-sm mb-1">💡 Key Recommendations</div>
                              <div className="text-xs text-yellow-800">
                                {agentInsight.keyRecommendations.slice(0, 2).map((rec: string, idx: number) => (
                                  <div key={idx} className="flex items-start">
                                    <span className="text-yellow-600 mr-1">•</span>
                                    {rec.length > 50 ? rec.substring(0, 50) + '...' : rec}
                                  </div>
                                ))}
                                {agentInsight.keyRecommendations.length > 2 && (
                                  <div className="text-yellow-600 mt-1">+{agentInsight.keyRecommendations.length - 2} more</div>
                                )}
                              </div>
                            </div>
                          )}

                          <div className="mt-3 flex items-center justify-between text-xs text-gray-600">
                            <span>Confidence: {Math.round((agentInsight.confidence || 0) * 100)}%</span>
                            <span>Time: {agentInsight.processingTime || 'N/A'}s</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
          </div>
        </div>
      )}

      {/* Generated Artifacts with Version History */}
      {results.artifacts.length > 0 && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-4">📝 Generated Content & Artifacts</h3>
          <div className="space-y-4">
            {results.artifacts.map(artifact => {
              const isRegenerated = artifact.metadata?.isRegeneration;
              const version = artifact.metadata?.version || 'v1';
              const regenerationAttempts = results.execution.metadata?.regenerationAttempts || 0;

              return (
                <div key={artifact.id} className={`border rounded-lg p-4 ${
                  isRegenerated ? 'bg-orange-50 border-orange-200' : 'bg-white border-gray-200'
                }`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium">{artifact.title}</h4>
                      {isRegenerated && (
                        <span className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">
                          🔄 Regenerated ({version})
                        </span>
                      )}
                      {regenerationAttempts > 0 && (
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                          {regenerationAttempts} iteration{regenerationAttempts > 1 ? 's' : ''}
                        </span>
                      )}
                    </div>
                    <div className="flex gap-2 text-xs">
                      <span className="bg-gray-100 px-2 py-1 rounded">{artifact.type}</span>
                      <span className={`px-2 py-1 rounded ${getStatusColor(artifact.status)}`}>
                      {artifact.status}
                    </span>
                    {artifact.approvedBy && (
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded">
                        Approved by {artifact.approvedBy}
                      </span>
                    )}
                  </div>
                </div>

                {/* Content Metadata */}
                {artifact.metadata && (
                  <div className="mb-4 grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    {artifact.metadata.wordCount && (
                      <div className="bg-blue-50 p-2 rounded text-center">
                        <div className="font-medium text-blue-900">{artifact.metadata.wordCount}</div>
                        <div className="text-xs text-blue-700">Words</div>
                      </div>
                    )}
                    {artifact.metadata.readingTime && (
                      <div className="bg-green-50 p-2 rounded text-center">
                        <div className="font-medium text-green-900">{artifact.metadata.readingTime}</div>
                        <div className="text-xs text-green-700">Reading Time</div>
                      </div>
                    )}
                    {artifact.metadata.seoScore && (
                      <div className="bg-purple-50 p-2 rounded text-center">
                        <div className="font-medium text-purple-900">{artifact.metadata.seoScore}/100</div>
                        <div className="text-xs text-purple-700">SEO Score</div>
                      </div>
                    )}
                    {artifact.metadata.generatedWith && (
                      <div className="bg-gray-50 p-2 rounded text-center">
                        <div className="font-medium text-gray-900 text-xs">{artifact.metadata.generatedWith}</div>
                        <div className="text-xs text-gray-700">Generated With</div>
                      </div>
                    )}
                  </div>
                )}

                {/* User Feedback History */}
                {isRegenerated && results.execution.metadata?.lastRejectionFeedback && (
                  <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <h5 className="font-medium text-yellow-900 mb-2">📝 User Feedback Incorporated:</h5>
                    <p className="text-sm text-yellow-800 italic">"{results.execution.metadata.lastRejectionFeedback}"</p>
                    <div className="mt-2 text-xs text-yellow-700">
                      This version was regenerated to address the above feedback and improve content quality.
                    </div>
                  </div>
                )}

                {/* Agent Enhancement Indicators */}
                {artifact.metadata?.enhancedWithAgents && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                    <h5 className="font-medium text-blue-900 mb-2">🤖 AI Agent Enhancements:</h5>
                    <div className="flex flex-wrap gap-2 text-xs">
                      {artifact.metadata.agentInsightsApplied && (
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {artifact.metadata.agentInsightsApplied} insights applied
                        </span>
                      )}
                      {artifact.metadata.seoOptimized && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                          SEO Optimized
                        </span>
                      )}
                      {artifact.metadata.strategicallyPlanned && (
                        <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                          Strategic Planning
                        </span>
                      )}
                      {artifact.metadata.marketResearched && (
                        <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded">
                          Market Research
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="bg-gray-50 p-3 rounded border">
                  <div className="flex items-center justify-between mb-2">
                    <h5 className="font-medium text-gray-900">Content Preview</h5>
                    <span className="text-xs text-gray-600">
                      {typeof artifact.content === 'string' ? artifact.content.length : 'N/A'} characters
                    </span>
                  </div>
                  <div className="max-h-40 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                      {formatContent(artifact.content)}
                    </pre>
                  </div>
                </div>

                {/* CMS Publishing Section */}
                {isPublishable(artifact) && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded">
                    <h5 className="font-medium text-blue-800 mb-3">Ready for Publishing</h5>

                    {/* Publishing Status */}
                    <div className="mb-3">
                      <span className="text-sm text-gray-600">Status: </span>
                      <span className={`text-sm font-medium ${
                        publishingStatus[artifact.id] === 'published' ? 'text-green-600' :
                        publishingStatus[artifact.id] === 'failed' ? 'text-red-600' :
                        publishingStatus[artifact.id] === 'publishing' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        {getPublishStatus(artifact.id)}
                      </span>
                    </div>

                    {/* Published URL */}
                    {publishResults[artifact.id]?.url && (
                      <div className="mb-3 p-2 bg-green-50 border border-green-200 rounded">
                        <span className="text-sm text-green-700">Published at: </span>
                        <a
                          href={publishResults[artifact.id].url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          {publishResults[artifact.id].url}
                        </a>
                      </div>
                    )}

                    {/* Error Message */}
                    {publishResults[artifact.id]?.error && (
                      <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded">
                        <span className="text-sm text-red-700">Error: {publishResults[artifact.id].error}</span>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={() => publishToCMS(artifact.id, artifact)}
                        disabled={publishingStatus[artifact.id] === 'publishing'}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {publishingStatus[artifact.id] === 'publishing' ? 'Publishing...' : '📤 Publish to CMS'}
                      </button>
                      <button
                        onClick={() => downloadArtifact(artifact)}
                        className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
                      >
                        📥 Download
                      </button>
                    </div>
                  </div>
                )}

                {/* Not Approved Message */}
                {!isPublishable(artifact) && (
                  <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded">
                    <p className="text-sm text-gray-600">
                      {artifact.status === 'pending_approval' ?
                        '⏳ Waiting for approval before publishing' :
                        `❌ Cannot publish - Status: ${artifact.status}`
                      }
                    </p>
                  </div>
                )}

                {/* Artifact Metadata */}
                <div className="mt-3 text-xs text-gray-500">
                  <div className="flex gap-4 flex-wrap">
                    <span>Created: {formatDate(artifact.createdAt)}</span>
                    {artifact.approvedAt && (
                      <span>Approved: {formatDate(artifact.approvedAt)}</span>
                    )}
                    <span>Type: {artifact.type || 'Unknown'}</span>
                    <span>Step: {artifact.stepId || 'Unknown'}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Step Details */}
      <div>
        <h3 className="text-xl font-semibold mb-4">Step Details</h3>
        <div className="space-y-3">
          {results.steps.map(step => (
            <div key={step.stepId} className="bg-white border rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium">{step.stepId}</h4>
                <div className="flex gap-2 text-xs">
                  <span className={`px-2 py-1 rounded ${
                    step.status === 'completed' ? 'bg-green-100 text-green-700' :
                    step.status === 'failed' ? 'bg-red-100 text-red-700' :
                    step.status === 'running' ? 'bg-blue-100 text-blue-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {step.status}
                  </span>
                  <span className="bg-gray-100 px-2 py-1 rounded">
                    {formatDuration(step.duration)}
                  </span>
                </div>
              </div>

              {step.error && (
                <div className="mb-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                  Error: {step.error}
                </div>
              )}

              {/* Feedback Analysis Display */}
              {step.outputs?.feedback_analysis && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
                  <h5 className="font-medium text-blue-900 mb-2">🧠 Feedback Analysis</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                    <div className="bg-white p-2 rounded">
                      <div className="font-medium text-blue-900">Action</div>
                      <div className="text-blue-700">{step.outputs.feedback_analysis.suggestedAction}</div>
                    </div>
                    <div className="bg-white p-2 rounded">
                      <div className="font-medium text-blue-900">Severity</div>
                      <div className={`${
                        step.outputs.feedback_analysis.severity === 'major' ? 'text-red-700' :
                        step.outputs.feedback_analysis.severity === 'moderate' ? 'text-yellow-700' :
                        'text-green-700'
                      }`}>{step.outputs.feedback_analysis.severity}</div>
                    </div>
                    <div className="bg-white p-2 rounded">
                      <div className="font-medium text-blue-900">Confidence</div>
                      <div className="text-blue-700">{Math.round(step.outputs.feedback_analysis.confidence * 100)}%</div>
                    </div>
                    <div className="bg-white p-2 rounded">
                      <div className="font-medium text-blue-900">Categories</div>
                      <div className="text-blue-700">{step.outputs.feedback_analysis.feedbackCategories?.length || 0}</div>
                    </div>
                  </div>
                  {step.outputs.feedback_analysis.keyIssues && step.outputs.feedback_analysis.keyIssues.length > 0 && (
                    <div className="mt-3">
                      <div className="font-medium text-blue-900 mb-1">Key Issues Identified:</div>
                      <ul className="text-xs text-blue-800 space-y-1">
                        {step.outputs.feedback_analysis.keyIssues.map((issue: string, idx: number) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-blue-600 mr-1">•</span>
                            {issue}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Review Decision Display */}
              {step.outputs?.review_decision && (
                <div className={`mb-4 p-3 rounded border ${
                  step.outputs.review_decision === 'approved' ? 'bg-green-50 border-green-200' :
                  step.outputs.review_decision === 'regenerated' ? 'bg-orange-50 border-orange-200' :
                  step.outputs.review_decision === 'rejected_continue' ? 'bg-yellow-50 border-yellow-200' :
                  'bg-gray-50 border-gray-200'
                }`}>
                  <h5 className={`font-medium mb-2 ${
                    step.outputs.review_decision === 'approved' ? 'text-green-900' :
                    step.outputs.review_decision === 'regenerated' ? 'text-orange-900' :
                    step.outputs.review_decision === 'rejected_continue' ? 'text-yellow-900' :
                    'text-gray-900'
                  }`}>
                    📝 Review Decision: {step.outputs.review_decision}
                  </h5>
                  {step.outputs.regeneration_applied && (
                    <div className="text-xs text-orange-800 mb-2">
                      ✅ Content was regenerated based on feedback
                    </div>
                  )}
                  {step.outputs.rejection_reason && (
                    <div className="text-xs text-yellow-800">
                      <strong>Reason:</strong> {step.outputs.rejection_reason}
                    </div>
                  )}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500 font-medium">Inputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {formatContent(step.inputs)}
                  </pre>
                </div>
                <div>
                  <span className="text-gray-500 font-medium">Outputs:</span>
                  <pre className="mt-1 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {formatContent(step.outputs)}
                  </pre>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Main export with Error Boundary
export default function WorkflowResultsPage() {
  return (
    <ErrorBoundary
      fallback={
        <div className="max-w-6xl mx-auto p-6">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <h3 className="font-bold">Application Error</h3>
            <p>Something went wrong while loading the workflow results.</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
            >
              🔄 Reload Page
            </button>
          </div>
        </div>
      }
    >
      <WorkflowResultsPageContent />
    </ErrorBoundary>
  );
}
